{"name": "template-webpack", "version": "3.2.1", "main": "src/main.js", "scripts": {"dev": "node log.js dev & webpack-dev-server --config webpack/config.js --open", "build": "node log.js build & webpack --config webpack/config.prod.js", "dev-nolog": "webpack-dev-server --config webpack/config.js --open", "build-nolog": "webpack --config webpack/config.prod.js"}, "repository": {"type": "git", "url": "git+https://github.com/phaserjs/template-webpack.git"}, "author": "Phaser Studio <<EMAIL>> (https://phaser.io/)", "license": "MIT", "licenseUrl": "http://www.opensource.org/licenses/mit-license.php", "bugs": {"url": "https://github.com/phaserjs/template-webpack/issues"}, "homepage": "https://github.com/phaserjs/template-webpack#readme", "devDependencies": {"@babel/core": "^7.24.5", "@babel/preset-env": "^7.24.5", "babel-loader": "^9.1.3", "clean-webpack-plugin": "^4.0.0", "copy-webpack-plugin": "^12.0.2", "file-loader": "^6.2.0", "html-webpack-plugin": "^5.6.0", "raw-loader": "^4.0.2", "terser-webpack-plugin": "^5.3.10", "webpack": "^5.91.0", "webpack-cli": "^5.1.4", "webpack-dev-server": "^5.0.4", "webpack-merge": "^5.10.0"}, "dependencies": {"phaser": "^3.88.2"}}